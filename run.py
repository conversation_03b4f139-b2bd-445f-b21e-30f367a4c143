from app import create_app
from app.models import db

app = create_app()

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        from app.models.user import User
        admin = User.query.filter_by(email='<EMAIL>').first()
        if not admin:
            admin_user = User(
                email='<EMAIL>',
                password='admin123',
                role='admin'
            )
            db.session.add(admin_user)
            db.session.commit()
            print("Admin user created: <EMAIL> / admin123")

    app.run(debug=True, host='0.0.0.0', port=5000)
