from app import db
from datetime import datetime

class Event(db.Model):
    __tablename__ = 'events'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=False)
    date_time = db.Column(db.DateTime, nullable=False)
    location = db.Column(db.String(200), nullable=False)
    total_seats = db.Column(db.Integer, nullable=False)
    available_seats = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    bookings = db.relationship('Booking', backref='event', lazy=True, cascade='all, delete-orphan')

    def __init__(self, title, description, date_time, location, total_seats):
        self.title = title
        self.description = description
        self.date_time = date_time
        self.location = location
        self.total_seats = total_seats
        self.available_seats = total_seats

    def book_seat(self):
        if self.available_seats > 0:
            self.available_seats -= 1
            return True
        return False

    def cancel_seat(self):
        if self.available_seats < self.total_seats:
            self.available_seats += 1
            return True
        return False

    def is_available(self):
        return self.available_seats > 0

    def is_past_event(self):
        return self.date_time < datetime.utcnow()

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'date_time': self.date_time.isoformat(),
            'location': self.location,
            'total_seats': self.total_seats,
            'available_seats': self.available_seats,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    def __repr__(self):
        return f'<Event {self.title}>'
