from app import db
from datetime import datetime

class Booking(db.Model):
    __tablename__ = 'bookings'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    event_id = db.Column(db.Integer, db.<PERSON>ey('events.id'), nullable=False)
    booking_date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='active')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (db.UniqueConstraint('user_id', 'event_id', name='unique_user_event_booking'),)

    def __init__(self, user_id, event_id):
        self.user_id = user_id
        self.event_id = event_id
        self.status = 'active'

    def cancel(self):
        if self.status == 'active':
            self.status = 'cancelled'
            return True
        return False

    def is_active(self):
        return self.status == 'active'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'event_id': self.event_id,
            'booking_date': self.booking_date.isoformat(),
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    def to_dict_with_details(self):
        booking_dict = self.to_dict()
        if self.event:
            booking_dict['event'] = self.event.to_dict()
        if self.user:
            booking_dict['user'] = {
                'id': self.user.id,
                'email': self.user.email
            }
        return booking_dict

    def __repr__(self):
        return f'<Booking User:{self.user_id} Event:{self.event_id}>'
